('C:\\workspace\\jupyter\\python-test\\build\\main\\PYZ-00.pyz',
 [('PIL',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'C:\\software\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\software\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\software\\python\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'C:\\software\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('_markupbase', 'C:\\software\\python\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_py_abc', 'C:\\software\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\software\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\software\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\software\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\software\\python\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\software\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\software\\python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\software\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\software\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\software\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\software\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\software\\python\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\software\\python\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\software\\python\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\software\\python\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\software\\python\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\software\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\software\\python\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\software\\python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\software\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'C:\\software\\python\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\software\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\software\\python\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\software\\python\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\software\\python\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\software\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\software\\python\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\software\\python\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\software\\python\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\software\\python\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\software\\python\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\software\\python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'C:\\software\\python\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\software\\python\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\software\\python\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\software\\python\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\software\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\software\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\software\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\software\\python\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\software\\python\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\software\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('bs4',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\software\\python\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\software\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\software\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'C:\\software\\python\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\software\\python\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'C:\\software\\python\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\software\\python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\software\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'C:\\software\\python\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'C:\\software\\python\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\software\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\software\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\software\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\software\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'C:\\software\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\software\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\software\\python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\software\\python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\software\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\software\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\software\\python\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\software\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\software\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\software\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\software\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\software\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\software\\python\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\software\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\software\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\software\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\software\\python\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'C:\\software\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\software\\python\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\software\\python\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\software\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\software\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\software\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\software\\python\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\software\\python\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\software\\python\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'C:\\software\\python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\software\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\software\\python\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'C:\\software\\python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\software\\python\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\software\\python\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'C:\\software\\python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\software\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\software\\python\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'C:\\software\\python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\software\\python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\software\\python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\software\\python\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\software\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'C:\\software\\python\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\software\\python\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\software\\python\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'C:\\software\\python\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'C:\\software\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\software\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\software\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\software\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\software\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\software\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\software\\python\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\software\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\software\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\software\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\software\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\software\\python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'C:\\software\\python\\Lib\\html\\parser.py', 'PYMODULE'),
  ('html5lib',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\__init__.py',
   'PYMODULE'),
  ('html5lib._ihatexml',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('html5lib._inputstream',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('html5lib._tokenizer',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('html5lib._trie',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('html5lib._trie._base',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('html5lib._trie.py',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('html5lib._utils',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\_utils.py',
   'PYMODULE'),
  ('html5lib.constants',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\constants.py',
   'PYMODULE'),
  ('html5lib.filters',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('html5lib.filters.alphabeticalattributes',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('html5lib.filters.base',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('html5lib.filters.inject_meta_charset',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('html5lib.filters.optionaltags',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('html5lib.filters.sanitizer',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('html5lib.filters.whitespace',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('html5lib.html5parser',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('html5lib.serializer',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\serializer.py',
   'PYMODULE'),
  ('html5lib.treebuilders',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.base',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('html5lib.treebuilders.dom',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree_lxml',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('html5lib.treewalkers.base',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('html5lib.treewalkers.dom',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree_lxml',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers.genshi',
   'C:\\software\\python\\Lib\\site-packages\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('http', 'C:\\software\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\software\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\software\\python\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server', 'C:\\software\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib',
   'C:\\software\\python\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\software\\python\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\software\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\software\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\software\\python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\software\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\software\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\software\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\software\\python\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\software\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\software\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\software\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\software\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\software\\python\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\software\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\software\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\software\\python\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\software\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\software\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\software\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\software\\python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\software\\python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\software\\python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\software\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers',
   'C:\\software\\python\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\software\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\software\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\software\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\software\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\software\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\software\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\software\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\software\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\software\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\software\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\software\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\software\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\software\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\software\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\software\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\software\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\software\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\software\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\software\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\software\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\software\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\software\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\software\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\software\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\software\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\software\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\software\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\software\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('olefile',
   'C:\\software\\python\\Lib\\site-packages\\olefile\\__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   'C:\\software\\python\\Lib\\site-packages\\olefile\\olefile.py',
   'PYMODULE'),
  ('opcode', 'C:\\software\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\software\\python\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'C:\\software\\python\\Lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\software\\python\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\software\\python\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\software\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\software\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\software\\python\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\software\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'C:\\software\\python\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\software\\python\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\software\\python\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'C:\\software\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\software\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\software\\python\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\software\\python\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'C:\\software\\python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\software\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\software\\python\\Lib\\random.py', 'PYMODULE'),
  ('readline',
   'C:\\software\\python\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('runpy', 'C:\\software\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\software\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\software\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\software\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\software\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\software\\python\\Lib\\signal.py', 'PYMODULE'),
  ('six', 'C:\\software\\python\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'C:\\software\\python\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'C:\\software\\python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\software\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('soupsieve',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\software\\python\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'C:\\software\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\software\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\software\\python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\software\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\software\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\software\\python\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\software\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\software\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\software\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\software\\python\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'C:\\software\\python\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('token', 'C:\\software\\python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\software\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\software\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\software\\python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\software\\python\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\software\\python\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'C:\\software\\python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\software\\python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\software\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\software\\python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'C:\\software\\python\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'C:\\software\\python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\software\\python\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\software\\python\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\software\\python\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\software\\python\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util', 'C:\\software\\python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\software\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\software\\python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\software\\python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request',
   'C:\\software\\python\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\software\\python\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('webbrowser', 'C:\\software\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('webencodings',
   'C:\\software\\python\\Lib\\site-packages\\webencodings\\__init__.py',
   'PYMODULE'),
  ('webencodings.labels',
   'C:\\software\\python\\Lib\\site-packages\\webencodings\\labels.py',
   'PYMODULE'),
  ('webencodings.x_user_defined',
   'C:\\software\\python\\Lib\\site-packages\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('win32con',
   'C:\\software\\python\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\software\\python\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\software\\python\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'C:\\software\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\software\\python\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\software\\python\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\software\\python\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\software\\python\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\software\\python\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\software\\python\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\software\\python\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\software\\python\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\software\\python\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\software\\python\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\software\\python\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\software\\python\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\software\\python\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\software\\python\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\software\\python\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\software\\python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\software\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\software\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\software\\python\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\software\\python\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\software\\python\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\software\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\software\\python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'C:\\software\\python\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yaml',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'C:\\software\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\software\\python\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\software\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\software\\python\\Lib\\zipimport.py', 'PYMODULE')])
