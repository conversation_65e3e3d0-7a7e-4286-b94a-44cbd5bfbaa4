import os
import signal  
import sys
from time import sleep

import dashscope
from dotenv import load_dotenv
import pyaudio
from dashscope.audio.asr import *
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from voice_synthesizer import VoiceSynthesizer

load_dotenv()

dashscope.api_key = os.getenv('MODAL_API_KEY')

mic = None
stream = None

sample_rate = 16000  
channels = 1  
dtype = 'int16'  
format_pcm = 'pcm'  
block_size = 3200

pending = False
input_text = ""

llm = ChatOpenAI(
    base_url=os.getenv('MODAL_URL'),
    api_key=os.getenv('MODAL_API_KEY'),
    model=os.getenv('MODAL_NAME'),
)

mic = pyaudio.PyAudio()
stream = mic.open(format=pyaudio.paInt16,
                    channels=1,
                    rate=16000,
                    input=True)

historys = []
def chat_completions(user_input):
    print('user_input: ', user_input)
    synthesizer = VoiceSynthesizer()
    historys.append(HumanMessage(content=user_input))
    prompts = [
        SystemMessage(content="你是一个聊天助手，负责简短的回答用户的问题，并引导聊天继续下去。"),
        *historys,
    ]
    aggregate = None
    for chunk in llm.stream(prompts, stream_usage=True):
        synthesizer.call(chunk.content)
        aggregate = chunk.content if aggregate is None else aggregate + chunk.content
    historys.append(AIMessage(content=aggregate))
    synthesizer.complete()

class Callback(RecognitionCallback):
    def on_open(self) -> None:
        print('RecognitionCallback open.')

    def on_close(self) -> None:
        print('RecognitionCallback close.')

    def on_complete(self) -> None:
        print('RecognitionCallback completed.')  

    def on_error(self, message) -> None:
        print('RecognitionCallback task_id: ', message.request_id)
        print('RecognitionCallback error: ', message.message)

    def on_event(self, result: RecognitionResult) -> None:
        global input_text
        sentence = result.get_sentence()
        if 'text' in sentence:
            print('RecognitionCallback text: ', sentence['text'])
            if RecognitionResult.is_sentence_end(sentence):
                input_text += sentence['text']


def signal_handler(sig, frame):
    global pending
    global input_text
    print(input_text)
    if not pending:
        chat_completions(input_text)
        pending = True
        input_text = ""
    else:
        pending = False
        input_text = ""


if __name__ == '__main__':
    
    callback = Callback()
    recognition = Recognition(
        model='paraformer-realtime-v2',
        format=format_pcm,
        sample_rate=sample_rate,
        semantic_punctuation_enabled=False,
        callback=callback
    )
    recognition.start()

    signal.signal(signal.SIGINT, signal_handler)

    while True:
        if stream:
            data = stream.read(3200, exception_on_overflow=False)
            recognition.send_audio_frame(data)

    # stream.stop_stream()
    # stream.close()
    # mic.terminate()
    # Forcefully exit the program
    # sys.exit(0)