{"cells": [{"cell_type": "code", "execution_count": 1, "id": "23af3f3b-72ae-4a5a-bb44-284338963ebb", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv\n", "    \n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "id": "7162e7c5-906f-4cdc-9f76-1b49747cc179", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    temperature=0.0\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "528deb71-bc09-4013-ac9e-92aecc83df47", "metadata": {}, "outputs": [], "source": ["from langchain_community.embeddings import DashScopeEmbeddings\n", "embeddings = DashScopeEmbeddings(\n", "    dashscope_api_key=os.getenv('DASHSCOPE_API_KEY'),\n", "    model=\"text-embedding-v3\",\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "25467e8f-f74d-4ff6-a52f-8ac0693d9b19", "metadata": {}, "outputs": [], "source": ["from qdrant_client import QdrantClient\n", "\n", "client = QdrantClient(\n", "    url=os.getenv('QDRANT_URL'), \n", "    api_key=os.getenv('QDRANT_API_KEY')\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "6026b3a5-dbc4-4234-a7ca-86ddfd6b7ee7", "metadata": {}, "outputs": [], "source": ["from langchain_qdrant import QdrantVectorStore\n", "\n", "qdrant = QdrantVectorStore(\n", "    client=client,\n", "    collection_name=\"test_collection_doc\",\n", "    embedding=embeddings,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "7e0699c9-26f6-4c8f-b837-0d9e48c9aca1", "metadata": {}, "outputs": [], "source": ["from langchain.chains import create_retrieval_chain\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"\"\"Answer the following question based only on the provided context:\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\n", "Question: {input}\"\"\")\n", "\n", "document_chain = create_stuff_documents_chain(llm, prompt)\n", "retriever = qdrant.as_retriever()\n", "retrieval_chain = create_retrieval_chain(retriever=retriever, combine_docs_chain=document_chain)"]}, {"cell_type": "code", "execution_count": 28, "id": "4413a344-a01a-4ab2-ac02-ce1a355e989a", "metadata": {}, "outputs": [], "source": ["from langchain.tools import tool\n", "import requests\n", "import json\n", "\n", "\n", "@tool\n", "def course_info_query(query: str) -> str:\n", "    \"\"\"\n", "    查询课程信息的工具\n", "    参数：\n", "    - course (必填): 课程代码（100表示520课程，200表示二阶课程）\n", "    - city (选填): 城市名称（仅支持杭州、成都、武汉）\n", "    \n", "    返回接口响应结果或错误描述\n", "    \"\"\"    \n", "    try:\n", "        # 发送带证书验证的HTTPS请求（测试环境关闭验证）\n", "        # response = requests.get(\n", "        #     \"https://localhost:9000\",\n", "        #     params=params,\n", "        #     verify=False  # 生产环境应使用有效证书并设为True\n", "        # )\n", "        req = json.loads(query)\n", "        course = str(req['course'])\n", "        if course not in ['100','200']:\n", "            return \"错误：course参数必须为100或200\"\n", "        # response.raise_for_status()\n", "        # return response.text\n", "        return '杭州 2025-08-19'\n", "    except requests.exceptions.RequestException as e:\n", "        return f\"接口请求失败：{str(e)}\"\n", "    except Exception as e:\n", "        return f\"发生未知错误：{str(e)}\""]}, {"cell_type": "code", "execution_count": 8, "id": "9ccb5ade-e454-4aaf-94c7-34f2bbdf92a0", "metadata": {}, "outputs": [], "source": ["@tool\n", "def fuying_edu_info(query: str) -> str:\n", "    \"\"\"\n", "    当用户询问扶鹰教育公司介绍或520/二阶等课程信息时使用。\n", "    能够提供公司基本信息、课程特色、教学体系等详细信息。\n", "    \"\"\"\n", "    response = retrieval_chain.invoke({\"input\": query})\n", "    return response[\"answer\"]"]}, {"cell_type": "code", "execution_count": 29, "id": "753762fc-9bed-4328-a3bc-79a5bd637040", "metadata": {}, "outputs": [], "source": ["from langchain.agents import create_react_agent, AgentExecutor\n", "from langchain import hub\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "\n", "prompt = PromptTemplate(input_variables=['agent_scratchpad', 'chat_history', 'input', 'tool_names', 'tools'], input_types={}, partial_variables={}, metadata={'lc_hub_owner': 'hwchase17', 'lc_hub_repo': 'react-chat', 'lc_hub_commit_hash': '3ecd5f710db438a9cf3773c57d6ac8951eefd2cd9a9b2a0026a65a0893b86a6e'}, template='Assistant is a large language model trained by OpenAI.\\n\\nAssistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\\n\\nAssistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.\\n\\nOverall, Assistant is a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist.\\n\\nTOOLS:\\n------\\n\\nAssistant has access to the following tools:\\n\\n{tools}\\n\\nTo use a tool, please use the following format:\\n\\n```\\nThought: Do I need to use a tool? Yes\\nAction: the action to take, should be one of [{tool_names}]\\nAction Input: the input to the action\\nObservation: the result of the action\\n```\\n\\nWhen you have a response to say to the Human, or if you do not need to use a tool, you MUST use the format:\\n\\n```\\nThought: Do I need to use a tool? No\\nFinal Answer: [your response here]\\n```\\n\\nBegin!\\n\\nPrevious conversation history:\\n{chat_history}\\n\\nNew input: {input}\\n{agent_scratchpad}')\n", "\n", "tools=[course_info_query,fuying_edu_info]\n", "agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "db700ae0-43fa-4777-90ac-a9959a1f9f00", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['agent_scratchpad', 'chat_history', 'input', 'tool_names', 'tools'], input_types={}, partial_variables={}, metadata={'lc_hub_owner': 'hwchase17', 'lc_hub_repo': 'react-chat', 'lc_hub_commit_hash': '3ecd5f710db438a9cf3773c57d6ac8951eefd2cd9a9b2a0026a65a0893b86a6e'}, template='Assistant is a large language model trained by OpenAI.\\n\\nAssistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.\\n\\nAssistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.\\n\\nOverall, Assistant is a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist.\\n\\nTOOLS:\\n------\\n\\nAssistant has access to the following tools:\\n\\n{tools}\\n\\nTo use a tool, please use the following format:\\n\\n```\\nThought: Do I need to use a tool? Yes\\nAction: the action to take, should be one of [{tool_names}]\\nAction Input: the input to the action\\nObservation: the result of the action\\n```\\n\\nWhen you have a response to say to the Human, or if you do not need to use a tool, you MUST use the format:\\n\\n```\\nThought: Do I need to use a tool? No\\nFinal Answer: [your response here]\\n```\\n\\nBegin!\\n\\nPrevious conversation history:\\n{chat_history}\\n\\nNew input: {input}\\n{agent_scratchpad}')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "code", "execution_count": 30, "id": "7bb00c28-c88a-4d82-b56f-63c7a31c8767", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```\n", "```\n", "Thought: Do I need to use a tool? Yes\n", "Action: course_info_query\n", "Action Input: {\"course\": 100}\n", "\u001b[0m\u001b[36;1m\u001b[1;3m杭州 2025-08-19\u001b[0m\u001b[32;1m\u001b[1;3mThoughtThought: Do I need to use a tool? No\n", "Final Answer: 520课程的报名信息如下：课程将在杭州于2025年8月19日开课。具体的报名流程和详细信息，您可以访问扶鹰教育的官方网站或联系他们的客服进行咨询。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '520要如何报名？',\n", " 'chat_history': [],\n", " 'output': '520课程的报名信息如下：课程将在杭州于2025年8月19日开课。具体的报名流程和详细信息，您可以访问扶鹰教育的官方网站或联系他们的客服进行咨询。'}"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke({\"input\": \"520要如何报名？\", \"chat_history\": []})"]}, {"cell_type": "code", "execution_count": 12, "id": "8d932e3b-0f37-4641-89ba-a85f8f629b83", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'HumanMessage' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m agent_executor\u001b[38;5;241m.\u001b[39mstream(\n\u001b[1;32m----> 2\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\u001b[43mHumanMessage\u001b[49m(content\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m扶鹰教育是做什么的?\u001b[39m\u001b[38;5;124m\"\u001b[39m)]}\n\u001b[0;32m      3\u001b[0m ):\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;28mprint\u001b[39m(chunk)\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m----\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'HumanMessage' is not defined"]}], "source": ["for chunk in agent_executor.stream(\n", "    {\"messages\": [HumanMessage(content=\"扶鹰教育是做什么的?\")]}\n", "):\n", "    print(chunk)\n", "    print(\"----\")"]}, {"cell_type": "code", "execution_count": null, "id": "d5cb381a-3a63-4324-9b7a-76382e82e220", "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "id": "0e534b27-bd37-406f-b785-3ad8629e5a01", "metadata": {}, "outputs": [], "source": ["!pip install langgraph"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}