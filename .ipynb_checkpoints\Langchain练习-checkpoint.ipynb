{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0b868013-886c-41ab-bf8f-e40c278de108", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv, find_dotenv"]}, {"cell_type": "code", "execution_count": 3, "id": "c11ea2ba-3dae-4098-8992-185b204c940f", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv()"]}, {"cell_type": "code", "execution_count": 4, "id": "1c0f7fc9-8a32-477e-ae47-d9d3f2355e20", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    api_key=os.getenv('MODAL_API_KEY'),\n", "    base_url=os.getenv('MODAL_URL'),\n", "    model=os.getenv('MODAL_NAME'),\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 29, "id": "f65f1f5a-ca03-481e-93ec-240b3e118172", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    ('system', 'You are the technical writer'),\n", "    ('user', '{input}')\n", "])"]}, {"cell_type": "code", "execution_count": 30, "id": "80849656-9fbb-4d70-b08f-939b6d282519", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "output_parser = StrOutputParser()"]}, {"cell_type": "code", "execution_count": 31, "id": "63397b3a-515b-4672-a54a-345628eea49c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'“520”在中文网络文化中是一个表达爱意的数字谐音。因为“520”发音与“我爱你”相似，所以很多人用“520”来代表“我爱你”。特别是在5月20日这一天，很多人会庆祝“520”来表达对爱人、朋友或家人的爱意，类似于情人节的氛围。\\n\\n此外，“520”也常被用于网络聊天、社交媒体或礼物赠送中，成为一种浪漫的表达方式。'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = prompt | llm | output_parser\n", "chain.invoke({'input', '什么是520?'})"]}, {"cell_type": "code", "execution_count": 32, "id": "f8f45683-e3f9-49af-831a-34d5c5f8e685", "metadata": {}, "outputs": [{"data": {"text/plain": ["'根据提供的上下文，520是扶鹰教育的一门特色课程，名为“520学习法”。'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = ChatPromptTemplate.from_template(\"\"\"Answer the following question based only on the provided context:\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\n", "Question: {input}\"\"\")\n", "chain = prompt | llm | output_parser\n", "chain.invoke({'input': '什么是520?', 'context': '520学习法是扶鹰教育的一门特色课程'})"]}, {"cell_type": "code", "execution_count": null, "id": "f386c2f2-082d-473b-aed9-3c5f39d2ad67", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "58320cd5-743d-420a-a39d-c44a45292305", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from langchain_community.embeddings import DashScopeEmbeddings\n", "embeddings = DashScopeEmbeddings(\n", "    dashscope_api_key=os.getenv('DASHSCOPE_API_KEY'),\n", "    model=\"text-embedding-v3\",\n", "    # other params...\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "f6bacee6-f020-40f2-8f76-6c83ec6595ee", "metadata": {}, "outputs": [], "source": ["from qdrant_client import QdrantClient\n", "\n", "client = QdrantClient(\n", "    url=os.getenv('QDRANT_URL'), \n", "    api_key=os.getenv('QDRANT_API_KEY')\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "990b5a87-89be-401e-a1ed-ab059b318b08", "metadata": {}, "outputs": [], "source": ["from qdrant_client.models import Distance, VectorParams\n", "\n", "client.create_collection(\n", "    collection_name=\"test_collection_doc\",\n", "    vectors_config=VectorParams(size=1024, distance=Distance.COSINE),\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "a4937948-baee-47e4-ada9-a9cd7fcf3cf7", "metadata": {}, "outputs": [], "source": ["from langchain_qdrant import QdrantVectorStore\n", "\n", "qdrant = QdrantVectorStore(\n", "    client=client,\n", "    collection_name=\"test_collection_doc\",\n", "    embedding=embeddings,\n", ")"]}, {"cell_type": "code", "execution_count": 18, "id": "b2283292-5ebb-4e1b-ad5f-6f3f12ddea6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["['ee353f145a444514be59cc4dc157977e']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.documents import Document\n", "\n", "# document_1 = Document(\n", "#     page_content=\"520学习法是扶鹰教育的一门特色课程\",\n", "# )\n", "\n", "# document_2 = Document(\n", "#     page_content=\"2阶是针对家长的学习课程\",\n", "# )\n", "\n", "document_3 = Document(\n", "    page_content=\"杭州扶鹰教育科技有限公司是一家家庭教育企业，坐落于杭州上城区，在创始人王金海博士的带领下，致力于打造落地的家庭教育平台，以孩子学习成果为导向，历时8年，提供线上、线下家长与孩子共同学习成长的课程体系及平台，拥有课程开发及服务能力。\",\n", ")\n", "qdrant.add_documents([document_3], batch_size=2)"]}, {"cell_type": "code", "execution_count": 78, "id": "77006618-20f2-4cc1-8036-0e9314ef99e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "id": "58d5482a-1788-4218-a603-4c86e96107f7", "metadata": {}, "outputs": [], "source": ["# This chain takes a list of documents and formats them all into a prompt, then passes that prompt to an LLM.\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"\"\"Answer the following question based only on the provided context:\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\n", "Question: {input}\"\"\")\n", "\n", "document_chain = create_stuff_documents_chain(llm, prompt)"]}, {"cell_type": "code", "execution_count": 15, "id": "10b73ffc-b5ab-41c1-bc13-d5a333373f1e", "metadata": {}, "outputs": [], "source": ["from langchain.chains import create_retrieval_chain\n", "\n", "retriever = qdrant.as_retriever()\n", "retrieval_chain = create_retrieval_chain(retriever=retriever, combine_docs_chain=document_chain)"]}, {"cell_type": "code", "execution_count": 16, "id": "4837017a-73a9-4651-84d7-4624d90120e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think><think>\n", "嗯，我现在得回答这个问题：“什么是二阶？”。根据提供的上下文，两条信息分别是：2阶是针对家长的学习课程，而520学习法是扶鹰教育的特色课程。首先，我需要确认用户问的是“二阶”而不是“2阶”，不过可能只是写法不同。然后，看上下文第一条直接说明2阶是家长的学习课程，所以直接回答这个就行。第二条提到520学习法，是扶鹰的课程，但可能与二阶有关联吗？原文没有明确说明两者的关系，所以可能不需要提到520。因此，应该简单回答二阶是针对家长的学习课程，属于扶鹰教育的一部分。要注意简洁和准确，不使用上下文未提及的信息。</think>\n", "\n", "二阶是由扶鹰教育推出的针对家长的学习课程，旨在帮助家长提升教育理念和培养孩子的能力，属于其特色教育体系中的一部分。该课程与扶鹰教育的另一特色课程“520学习法”共同构成了家长学习成长的支持系统。\n"]}], "source": ["response = retrieval_chain.invoke({\"input\": \"什么是二阶?\"})\n", "print(response[\"answer\"])"]}, {"cell_type": "code", "execution_count": 17, "id": "6e5dd910-2382-404d-8d2c-0c7171b78dc5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': '什么是二阶?',\n", " 'context': [Document(metadata={'_id': '178dc210-914b-4eec-99e2-757a5ad0b7ca', '_collection_name': 'test_collection_doc'}, page_content='2阶是针对家长的学习课程'),\n", "  Document(metadata={'_id': '8a047441-cbc3-4f7b-98a0-009a6bc6b671', '_collection_name': 'test_collection_doc'}, page_content='520学习法是扶鹰教育的一门特色课程')],\n", " 'answer': '<think><think>\\n嗯，我现在得回答这个问题：“什么是二阶？”。根据提供的上下文，两条信息分别是：2阶是针对家长的学习课程，而520学习法是扶鹰教育的特色课程。首先，我需要确认用户问的是“二阶”而不是“2阶”，不过可能只是写法不同。然后，看上下文第一条直接说明2阶是家长的学习课程，所以直接回答这个就行。第二条提到520学习法，是扶鹰的课程，但可能与二阶有关联吗？原文没有明确说明两者的关系，所以可能不需要提到520。因此，应该简单回答二阶是针对家长的学习课程，属于扶鹰教育的一部分。要注意简洁和准确，不使用上下文未提及的信息。</think>\\n\\n二阶是由扶鹰教育推出的针对家长的学习课程，旨在帮助家长提升教育理念和培养孩子的能力，属于其特色教育体系中的一部分。该课程与扶鹰教育的另一特色课程“520学习法”共同构成了家长学习成长的支持系统。'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from langgraph.prebuilt import create_react_agent, AgentExecutor\n", "\n", "prompt = hub.pull(\"hwchase17/react\")\n", "tools = [introduce]\n", "agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "78881314-d556-46b2-9f45-974f46406556", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables.history import RunnableWithMessageHistory\n", "from langchain_core.chat_history import BaseChatMessageHistory\n", "\n", "store = {}\n", "\n", "def get_session_history(session_id: str) -> BaseChatMessageHistory:\n", "    if session_id not in store:\n", "        store[session_id] = ChatMessageHistory()\n", "    return store[session_id]\n", "    \n", "agent_with_chat_history = RunnableWithMessageHistory(\n", "    agent_executor,\n", "    get_session_history,\n", "    input_message_key=\"input\",\n", "    history_message_key=\"chat_history\"\n", ")\n", "response = agent_with_chat_history.invoke(\n", "    {\"input\": \"我是xxx\"},\n", "    config={\"configurable\": {\"session_id\": \"123\"}}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "68d8a16e-c1fe-479e-995d-3417766b0fef", "metadata": {}, "outputs": [], "source": ["from langchain.tools import BaseTool, StructuredTool, tool\n", "\n", "@tool\n", "def introduce(text: str) -> str:\n", "    '''用于介绍公司和课程的基本信息,当询问扶鹰公司和520、二阶等课程的介绍时应该使用该工具'''\n", "    return \"<PERSON>\"\n", "\n", "@tool\n", "def course(text: str) -> str:\n", "    '''当用户想要报名520、二阶等课程，或者想要查询"]}, {"cell_type": "code", "execution_count": null, "id": "818a77b8-ee93-4d0f-b4de-7c03480844b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c25ff293-2f2f-4bf6-937a-b40419fc5b18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "5dbcf836-eebb-4bf1-bab8-a9601bae963d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["operation_id=0 status=<UpdateStatus.COMPLETED: 'completed'>\n"]}], "source": ["query_result = embeddings.embed_query(\"520学习法是扶鹰教育的一门特色课程\")\n", "from qdrant_client.models import PointStruct\n", "\n", "operation_info = client.upsert(\n", "    collection_name=\"test_collection1\",\n", "    wait=True,\n", "    points=[\n", "        PointStruct(id=5, vector=query_result, payload={\"name\": \"520学习法是扶鹰教育的一门特色课程\"}),\n", "    ],\n", ")\n", "\n", "print(operation_info)"]}, {"cell_type": "code", "execution_count": 91, "id": "9f4bdb66-b46c-4e0c-8fb8-5fb288c276a2", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ScoredPoint(id='745a5ed0-43ce-43c4-88ad-668b3e95563a', version=1, score=0.67011726, payload={'page_content': '520学习法是扶鹰教育的一门特色课程', 'metadata': None}, vector=None, shard_key=None, order_value=None), ScoredPoint(id=5, version=0, score=0.67011726, payload={'name': '520学习法是扶鹰教育的一门特色课程'}, vector=None, shard_key=None, order_value=None)]\n"]}], "source": ["query_result = embeddings.embed_query(\"什么是520?\")\n", "\n", "search_result = client.query_points(\n", "    collection_name=\"test_collection1\",\n", "    query=query_result,\n", "    with_payload=True,\n", "    limit=3\n", ")\n", "\n", "print(search_result.points)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}