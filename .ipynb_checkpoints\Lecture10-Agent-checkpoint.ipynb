{"cells": [{"cell_type": "markdown", "id": "d7ebc849", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "5ee90a1b", "metadata": {}, "source": ["Agent：智能体\n", "Agent相关的技术其实还没有很成熟"]}, {"cell_type": "code", "execution_count": 61, "id": "72861784", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入openai api key\n", "import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "# .env 存储api_key\n", "load_dotenv()"]}, {"cell_type": "markdown", "id": "aacfbd19", "metadata": {}, "source": ["### 1. Functional Call of OpenAI\n", "\n", "之前我们讲解过pydantic object， 就是用来自定义LLM返回的格式，包括自定义的字段以及types. 然而，openAI自己也实现了这套逻辑，也叫做functional call， 就是返回用户自定义的Json的structure。 "]}, {"cell_type": "markdown", "id": "db31cb35", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 91, "id": "b87b5ead", "metadata": {}, "outputs": [], "source": ["from langchain_community.utils.openai_functions import (\n", "    convert_pydantic_to_openai_function,\n", ")\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field, validator\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 92, "id": "f0b2e462", "metadata": {}, "outputs": [], "source": ["llm = ChatOpenAI(model = \"gpt-4\", temperature=0)"]}, {"cell_type": "code", "execution_count": 93, "id": "34430885", "metadata": {}, "outputs": [], "source": ["from langchain_core.utils.function_calling import convert_to_openai_function\n", "\n", "class Discovery(BaseModel):\n", "    \"\"\"著名的科学家的发现\"\"\"\n", "\n", "    name: str = Field(description=\"科学家名字\")\n", "    discovery: str = Field(description=\"科学家的成就\")\n", "\n", "\n", "# 转换成openai_funtions, 其实就是json_schema\n", "openai_functions = [convert_to_openai_function(Discovery)]"]}, {"cell_type": "code", "execution_count": 95, "id": "f8c3e228", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '<PERSON>',\n", "  'description': '著名的科学家的发现',\n", "  'parameters': {'type': 'object',\n", "   'properties': {'name': {'description': '科学家名字', 'type': 'string'},\n", "    'discovery': {'description': '科学家的成就', 'type': 'string'}},\n", "   'required': ['name', 'discovery']}}]"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["openai_functions "]}, {"cell_type": "code", "execution_count": 96, "id": "003e31b1", "metadata": {}, "outputs": [], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [(\"system\", \"你是一个助手\"), (\"user\", \"{input}\")]\n", ")"]}, {"cell_type": "code", "execution_count": 97, "id": "6b0a6ad1", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers.openai_functions import JsonOutputFunctionsParser\n", "parser = JsonOutputFunctionsParser()"]}, {"cell_type": "code", "execution_count": 102, "id": "f9eceb4c", "metadata": {}, "outputs": [], "source": ["chain = prompt | llm.bind(functions=openai_functions) | parser"]}, {"cell_type": "code", "execution_count": 103, "id": "986c6f3b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': '阿尔伯特·爱因斯坦', 'discovery': '相对论'}"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"input\": \"讲一个著名科学家的事迹\"})"]}, {"cell_type": "code", "execution_count": null, "id": "af357d0c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "38cef7da", "metadata": {}, "source": ["### 2. <PERSON>\n", "#### a. Agent Basics"]}, {"cell_type": "markdown", "id": "0bc4b57b", "metadata": {}, "source": ["##### Q1: 什么是Agent? \n", "特性：\n", "- 解决比较复杂的问题， 如果我们的问题是一问一答，实际上不太需要Agent\n", "- 复杂的问题拆解成若干个简单的问题； 把每个简单的问题解决，之后整合成答案； （planning)\n", "- 会用到若干个工具（外部）。大模型训练的时候用到的数据是过时的，比如GPT-4数据应该是到23年4月份；网上的，实时的数据，如何获取？\n", "- 具体用什么工具来解决什么问题？（planning）\n", "\n", "Agent的例子：\n", "\n", "- 任务： ”生成一个短视频，讲述大模型在金融领域的应用，特别是24年落地的应用场景，生成出来的短视频脚本不能喂饭抖音的规定，不能出现特定的关键词“\n", "- 任务拆解（planning)：\n", "1. 拆解：由大模型来做\n", "2. 通过Bing API去搜索网页，“大模型+金融领域应用+24年落地+应用场景” （reasoning），搜索完之后，获取相应的内容Text（Action：search_for_bing (参数如上)， observation: 内容Text\n", "3. 基于Text，总结成300字以内的精华内容。 交给GPT去做总结 （Action: summarzation_by_GPT,  Observation: summaried text)\n", "\n", "- 4-5步是loop, 直到满足抖音的条件\n", "4. 对于生成的精华内容做进一步验证，判断是否违反了抖音的规则。 （Action: varified_by_douyin_api, Observation: 是/否）\n", "5. 假如第4步，返回的是“是”，重新生成，返回第四步骤；\n", "6. 把脚本分成10个镜头（Action: segment_script_by_GPT, Observation: 分镜之后的文本）\n", "7. 每个分镜通过调用Stable Diffusion生成图片（Action: call_sd, Parameters: 文本，sytle.. Observation: list of 图片）\n", "8. 图片合成视频（Action: generate_video_by_xxx_tool, output: video)\n", "\n", "- 角色： Agent （LLM）\n", "- Tools： 工具 （提前准备好的），只要知道工具的url, 输入参数，会返回相应的结果\n", "\n", "\n", "”任务：生成面向开发工程师的大模型课程“ \n", "- 1. 生成大纲，10章的内容，每个章节不少于5个小节\n", "- 2. 针对于每个小节，生成具体的内容，选择题，..\n", "- 3. xxxxxxxxx"]}, {"cell_type": "markdown", "id": "d47294df", "metadata": {}, "source": ["##### Q2: Agent和Chain之间的区别？\n", "同样点：都是一步一步完成一系列任务，从而最终完成复杂的任务。 \n", "不同点：\n", "- Chain是提前定义好的，也就是第一步要做什么，第二步要做什么，第三步骤要什么。适用于提前知道每一步具体要做什么事情。 静态的\n", "- 假如给Agent提供5个工具， 针对于不同的用户问题，任务的顺序是可能不一样的。 动态的； "]}, {"cell_type": "code", "execution_count": 104, "id": "68fd5d90", "metadata": {}, "outputs": [], "source": ["from langchain import hub\n", "from langchain.agents import AgentExecutor, create_react_agent, load_tools\n", "from langchain.agents.agent_types import AgentType"]}, {"cell_type": "code", "execution_count": 105, "id": "416b981e", "metadata": {}, "outputs": [], "source": ["tools = load_tools(\n", "    [\"llm-math\"], llm\n", ")"]}, {"cell_type": "code", "execution_count": 72, "id": "76c37105", "metadata": {}, "outputs": [], "source": ["prompt = hub.pull(\"hwchase17/react\")\n"]}, {"cell_type": "code", "execution_count": 73, "id": "858fbc05", "metadata": {}, "outputs": [], "source": ["agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n"]}, {"cell_type": "code", "execution_count": 74, "id": "b6a8760d", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI need to multiply these two numbers together.\n", "Action: Calculator\n", "Action Input: 134292 * 282393\u001b[0m\u001b[36;1m\u001b[1;3mAnswer: 37923120756\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 37923120756\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'What is 134292 times 282393?', 'output': '37923120756'}"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\n", "        \"input\": \"What is 134292 times 282393?\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "78efbd92", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4a387c7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "710d4aa9", "metadata": {}, "source": ["#### b. Python Agent tools"]}, {"cell_type": "code", "execution_count": 110, "id": "b4ccef9b", "metadata": {}, "outputs": [], "source": ["from langchain import hub\n", "from langchain.agents import AgentExecutor, create_react_agent, create_openai_functions_agent,load_tools\n", "from langchain_openai import ChatOpenAI\n", "\n", "\n", "llm = ChatOpenAI(temperature=0.0)\n", "tools = load_tools(\n", "    [\"arxiv\",\"wikipedia\"],\n", ")\n", "prompt = hub.pull(\"hwchase17/react\")\n", "\n", "agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 76, "id": "a19e9b95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI should use the arxiv tool to search for the paper with the identifier 1605.08386.\n", "Action: arxiv\n", "Action Input: 1605.08386\u001b[0m\u001b[36;1m\u001b[1;3mPublished: 2016-05-26\n", "Title: Heat-bath random walks with <PERSON><PERSON> bases\n", "Authors: <AUTHORS>\n", "Summary: Graphs on lattice points are studied whose edges come from a finite set of\n", "allowed moves of arbitrary length. We show that the diameter of these graphs on\n", "fibers of a fixed integer matrix can be bounded from above by a constant. We\n", "then study the mixing behaviour of heat-bath random walks on these graphs. We\n", "also state explicit conditions on the set of moves so that the heat-bath random\n", "walk, a generalization of the <PERSON><PERSON><PERSON> dynamics, is an expander in fixed\n", "dimension.\u001b[0m\u001b[32;1m\u001b[1;3mI now know the paper 1605.08386 is about heat-bath random walks with Markov bases.\n", "Final Answer: The paper 1605.08386 is about heat-bath random walks with Markov bases.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': \"What's the paper 1605.08386 about?\",\n", " 'output': 'The paper 1605.08386 is about heat-bath random walks with Markov bases.'}"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\n", "        \"input\": \"What's the paper 1605.08386 about?\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "624c1e0c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b4507226", "metadata": {}, "source": ["#### c. custom tools"]}, {"cell_type": "code", "execution_count": 77, "id": "93a55009", "metadata": {}, "outputs": [], "source": ["from langchain.tools import BaseTool, StructuredTool, tool\n", "\n", "@tool\n", "def coolest_guy(text: str) -> str:\n", "    '''Returns the name of the coolest guy in the universe'''\n", "    return \"<PERSON>\"\n"]}, {"cell_type": "code", "execution_count": 111, "id": "debddb85", "metadata": {}, "outputs": [], "source": ["tools = tools + [coolest_guy]"]}, {"cell_type": "code", "execution_count": 117, "id": "5af831ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper(wiki_client=<module 'wikipedia' from '/Users/<USER>/Documents/greedyai/LLMAppCourse/Lecture7/langchain-new/lib/python3.9/site-packages/wikipedia/__init__.py'>, top_k_results=3, lang='en', load_all_available_meta=False, doc_content_chars_max=4000))"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 118, "id": "c377692e", "metadata": {}, "outputs": [], "source": ["agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 123, "id": "379e1b8f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI should use the coolest_guy tool to find out who the coolest guy is.\n", "Action: coolest_guy\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3mMike\u001b[0m\u001b[32;1m\u001b[1;3mI should provide the final answer now.\n", "Final Answer: <PERSON>\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'Who is the coolest guy in the universe?', 'output': '<PERSON>'}"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\n", "        \"input\": \"Who is the coolest guy in the universe?\",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 134, "id": "74ab8f5a", "metadata": {}, "outputs": [], "source": ["@tool\n", "def some_api_call(text: str) -> str:\n", "    '''可以定义自己的API Call!'''\n", "    return api_result"]}, {"cell_type": "code", "execution_count": 135, "id": "edeeba6f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "@tool\n", "def get_time(text: str) -> str:\n", "    '''Returns the current time. Use this for any questions\n", "    regarding the current time. Input is an empty string and\n", "    the current time is returned in a string format. Only use this function\n", "    for the current time. Other time related questions should use another tool'''\n", "    return str(datetime.now())"]}, {"cell_type": "code", "execution_count": 136, "id": "afa23acf", "metadata": {}, "outputs": [], "source": ["tools = tools + [get_time]"]}, {"cell_type": "code", "execution_count": null, "id": "008a9705", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 137, "id": "3146c29b", "metadata": {}, "outputs": [], "source": ["agent = create_react_agent(llm, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 138, "id": "42bb1a5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI should use arxiv to search for the paper titled \"REACT: SYNERGIZING REASONING AND ACTING IN LANGUAGE MODELS\" and find its publication date.\n", "Action: arxiv\n", "Action Input: REACT: SYNER<PERSON>ZING REASONING AND ACTING IN LANGUAGE MODELS\u001b[0m\u001b[36;1m\u001b[1;3mPublished: 2023-12-05\n", "Title: Language Agent Tree Search Unifies Reasoning Acting and Planning in Language Models\n", "Authors: <AUTHORS>\n", "Summary: While large language models (LLMs) have demonstrated impressive performance\n", "on a range of decision-making tasks, they rely on simple acting processes and\n", "fall short of broad deployment as autonomous agents. We introduce LATS\n", "(Language Agent Tree Search), a general framework that synergizes the\n", "capabilities of LLMs in planning, acting, and reasoning. Drawing inspiration\n", "from Monte Carlo tree search in model-based reinforcement learning, LATS\n", "employs LLMs as agents, value functions, and optimizers, repurposing their\n", "latent strengths for enhanced decision-making. What is crucial in this method\n", "is the use of an environment for external feedback, which offers a more\n", "deliberate and adaptive problem-solving mechanism that moves beyond the\n", "limitations of existing techniques. Our experimental evaluation across diverse\n", "domains, such as programming, HotPotQA, and WebShop, illustrates the\n", "applicability of LATS for both reasoning and acting. In particular, LATS\n", "achieves 94.4% for programming on HumanEval with GPT-4 and an average score of\n", "75.9 for web browsing on WebShop with GPT-3.5, demonstrating the effectiveness\n", "and generality of our method.\n", "\n", "Published: 2023-03-10\n", "Title: ReAct: Synergizing Reasoning and Acting in Language Models\n", "Authors: <AUTHORS>\n", "Summary: While large language models (LLMs) have demonstrated impressive capabilities\n", "across tasks in language understanding and interactive decision making, their\n", "abilities for reasoning (e.g. chain-of-thought prompting) and acting (e.g.\n", "action plan generation) have primarily been studied as separate topics. In this\n", "paper, we explore the use of LLMs to generate both reasoning traces and\n", "task-specific actions in an interleaved manner, allowing for greater synergy\n", "between the two: reasoning traces help the model induce, track, and update\n", "action plans as well as handle exceptions, while actions allow it to interface\n", "with external sources, such as knowledge bases or environments, to gather\n", "additional information. We apply our approach, named ReAct, to a diverse set of\n", "language and decision making tasks and demonstrate its effectiveness over\n", "state-of-the-art baselines, as well as improved human interpretability and\n", "trustworthiness over methods without reasoning or acting components.\n", "Concretely, on question answering (HotpotQA) and fact verification (Fever),\n", "ReAct overcomes issues of hallucination and error propagation prevalent in\n", "chain-of-thought reasoning by interacting with a simple Wikipedia API, and\n", "generates human-like task-solving trajectories that are more interpretable than\n", "baselines without reasoning traces. On two interactive decision making\n", "benchmarks (ALFWorld and WebShop), ReAct outperforms imitation and\n", "reinforcement learning methods by an absolute success rate of 34% and 10%\n", "respectively, while being prompted with only one or two in-context examples.\n", "Project site with code: https://react-lm.github.io\n", "\n", "Published: 2021-09-03\n", "Title: CR-Walker: Tree-Structured Graph Reasoning and Dialog Acts for Conversational Recommendation\n", "Authors: <AUTHORS>\n", "Summary: Growing interests have been attracted in Conversational Recommender Systems\n", "(CRS), which explore user preference through conversational interactions in\n", "order to make appropriate recommendation. However, there is still a lack of\n", "ability in existing CRS to (1) traverse multiple reasoning paths over\n", "background knowledge to introduce relevant items and attributes, and (2)\n", "arrange selected entities appropriately under current system intents to control\n", "response generation. To address these issues, we propose CR-Walker in this\n", "paper, a model that performs tree-structured reasoning on a knowledge graph,\n", "and generates informative dial\u001b[0m\u001b[32;1m\u001b[1;3mThe paper titled \"REACT: SYNERGIZING REASONING AND ACTING IN LANGUAGE MODELS\" was published on 2023-03-10.\n", "Action: get_time\n", "Action Input: \u001b[0m\u001b[33;1m\u001b[1;3m2024-03-05 21:32:00.186634\u001b[0m\u001b[32;1m\u001b[1;3mThe paper \"REACT: SYNERGIZING REASONING AND ACTING IN LANGUAGE MODELS\" was published on March 10, 2023. \n", "Final Answer: 1 month has passed since the paper was published.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'How many months past since when the paper titled REACT: SYNERGIZING REASONING AND ACTING IN LANGUAGE MODELS been published?',\n", " 'output': '1 month has passed since the paper was published.'}"]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\n", "        #\"input\": \"How many years has been past since <PERSON> died?\",\n", "        \"input\": \"How many months past since when the paper titled REACT: SYNERGIZING REASONING AND ACTING IN LANGUAGE MODELS been published?\"\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 139, "id": "7a09a7c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mI should use the get_time tool to get the current time.\n", "Action: get_time\n", "Action Input: \u001b[0m\u001b[33;1m\u001b[1;3m2024-03-05 21:33:35.607739\u001b[0m\u001b[32;1m\u001b[1;3mI have the current time now. \n", "\n", "Final Answer: The current time is 2024-03-05 21:33:35.607739\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'what time is it now?',\n", " 'output': 'The current time is 2024-03-05 21:33:35.607739'}"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\n", "        \"input\": \"what time is it now?\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "cdf0da06", "metadata": {}, "source": ["![Pirate](images/WeChatWorkScreenshot_74c06a19-341b-4d8d-b699-6e01b0fa2b7f.png)"]}, {"cell_type": "markdown", "id": "30a100e3", "metadata": {}, "source": ["#### ReAct from scratch"]}, {"cell_type": "code", "execution_count": 142, "id": "12efa9fe", "metadata": {}, "outputs": [], "source": ["import openai\n", "import re\n", "import httpx\n", "\n", " \n", "class ChatBot:\n", "    def __init__(self, system=\"\"):\n", "        self.system = system\n", "        self.messages = []\n", "        if self.system:\n", "            self.messages.append({\"role\": \"system\", \"content\": system})\n", "    \n", "    def __call__(self, message):\n", "        self.messages.append({\"role\": \"user\", \"content\": message})\n", "        result = self.execute()\n", "        self.messages.append({\"role\": \"assistant\", \"content\": result})\n", "        return result\n", "    \n", "    def execute(self):\n", "        completion = llm.invoke(self.messages)\n", "        return completion.content\n", "\n", "prompt = \"\"\"\n", "You run in a loop of Thought, Action, PAUSE, Observation.\n", "At the end of the loop you output an Answer\n", "Use Thought to describe your thoughts about the question you have been asked.\n", "Use Action to run one of the actions available to you - then return PAUSE.\n", "Observation will be the result of running those actions.\n", "\n", "Your available actions are:\n", "\n", "calculate:\n", "e.g. calculate: 4 * 7 / 3\n", "Runs a calculation and returns the number - uses Python so be sure to use floating point syntax if necessary\n", "\n", "wikipedia:\n", "e.g. wikipedia: Django\n", "Returns a summary from searching Wikipedia\n", "\n", "simon_blog_search:\n", "e.g. simon_blog_search: Django\n", "<PERSON>'s blog for that term\n", "\n", "Always look things up on Wikipedia if you have the opportunity to do so.\n", "\n", "Example session:\n", "\n", "Question: What is the capital of France?\n", "Thought: I should look up France on Wikipedia\n", "Action: wikipedia: France\n", "PAUSE\n", "\n", "You will be called again with this:\n", "\n", "Observation: France is a country. The capital is Paris.\n", "\n", "You then output:\n", "\n", "Answer: The capital of France is Paris\n", "\"\"\".strip()\n", "\n", "\n", "action_re = re.compile('^Action: (\\w+): (.*)$')\n", "\n", "def query(question, max_turns=5):\n", "    i = 0\n", "    bot = ChatBot(prompt)\n", "    next_prompt = question\n", "    while i < max_turns:\n", "        i += 1\n", "        result = bot(next_prompt)\n", "        print(result)\n", "        print (bot.messages)\n", "        actions = [action_re.match(a) for a in result.split('\\n') if action_re.match(a)]\n", "        if actions:\n", "            # There is an action to run\n", "            action, action_input = actions[0].groups()\n", "            if action not in known_actions:\n", "                raise Exception(\"Unknown action: {}: {}\".format(action, action_input))\n", "            print(\" -- running {} {}\".format(action, action_input))\n", "            observation = known_actions[action](action_input)\n", "            print(\"Observation:\", observation)\n", "            next_prompt = \"Observation: {}\".format(observation)\n", "        else:\n", "            return\n", "\n", "\n", "def wikipedia(q):\n", "    return httpx.get(\"https://en.wikipedia.org/w/api.php\", params={\n", "        \"action\": \"query\",\n", "        \"list\": \"search\",\n", "        \"srsearch\": q,\n", "        \"format\": \"json\"\n", "    }).json()[\"query\"][\"search\"][0][\"snippet\"]\n", "\n", "\n", "def simon_blog_search(q):\n", "    results = httpx.get(\"https://datasette.simonwillison.net/simonwillisonblog.json\", params={\n", "        \"sql\": \"\"\"\n", "        select\n", "          blog_entry.title || ': ' || substr(html_strip_tags(blog_entry.body), 0, 1000) as text,\n", "          blog_entry.created\n", "        from\n", "          blog_entry join blog_entry_fts on blog_entry.rowid = blog_entry_fts.rowid\n", "        where\n", "          blog_entry_fts match escape_fts(:q)\n", "        order by\n", "          blog_entry_fts.rank\n", "        limit\n", "          1\"\"\".strip(),\n", "        \"_shape\": \"array\",\n", "        \"q\": q,\n", "    }).json()\n", "    return results[0][\"text\"]\n", "\n", "def calculate(what):\n", "    return eval(what)\n", "\n", "known_actions = {\n", "    \"wikipedia\": wikipedia,\n", "    \"calculate\": calculate,\n", "    \"simon_blog_search\": simon_blog_search\n", "}"]}, {"cell_type": "code", "execution_count": 144, "id": "5ea4fec7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thought: I should look up the neighboring countries of England on Wikipedia.\n", "\n", "Action: wikipedia: England\n", "PAUSE\n", "[{'role': 'system', 'content': \"You run in a loop of Thought, Action, PAUSE, Observation.\\nAt the end of the loop you output an Answer\\nUse Thought to describe your thoughts about the question you have been asked.\\nUse Action to run one of the actions available to you - then return PAUSE.\\nObservation will be the result of running those actions.\\n\\nYour available actions are:\\n\\ncalculate:\\ne.g. calculate: 4 * 7 / 3\\nRuns a calculation and returns the number - uses Python so be sure to use floating point syntax if necessary\\n\\nwikipedia:\\ne.g. wikipedia: Django\\nReturns a summary from searching Wikipedia\\n\\nsimon_blog_search:\\ne.g. simon_blog_search: <PERSON><PERSON><PERSON>\\nSearch <PERSON>'s blog for that term\\n\\nAlways look things up on Wikipedia if you have the opportunity to do so.\\n\\nExample session:\\n\\nQuestion: What is the capital of France?\\nThought: I should look up France on Wikipedia\\nAction: wikipedia: France\\nPAUSE\\n\\nYou will be called again with this:\\n\\nObservation: France is a country. The capital is Paris.\\n\\nYou then output:\\n\\nAnswer: The capital of France is Paris\"}, {'role': 'user', 'content': 'What does England share borders with? #'}, {'role': 'assistant', 'content': 'Thought: I should look up the neighboring countries of England on Wikipedia.\\n\\nAction: wikipedia: England\\nPAUSE'}]\n", " -- running wikipedia England\n", "Observation: <span class=\"searchmatch\">England</span> is a country that is part of the United Kingdom. The country is located on the island of Great Britain, of which it covers roughly 62%, and over\n", "Thought: I need to find the information about the neighboring countries of England in the Wikipedia summary.\n", "\n", "Action: wikipedia: Neighbouring countries of England\n", "PAUSE\n", "[{'role': 'system', 'content': \"You run in a loop of Thought, Action, PAUSE, Observation.\\nAt the end of the loop you output an Answer\\nUse Thought to describe your thoughts about the question you have been asked.\\nUse Action to run one of the actions available to you - then return PAUSE.\\nObservation will be the result of running those actions.\\n\\nYour available actions are:\\n\\ncalculate:\\ne.g. calculate: 4 * 7 / 3\\nRuns a calculation and returns the number - uses Python so be sure to use floating point syntax if necessary\\n\\nwikipedia:\\ne.g. wikipedia: Django\\nReturns a summary from searching Wikipedia\\n\\nsimon_blog_search:\\ne.g. simon_blog_search: Djan<PERSON>\\nSearch <PERSON>'s blog for that term\\n\\nAlways look things up on Wikipedia if you have the opportunity to do so.\\n\\nExample session:\\n\\nQuestion: What is the capital of France?\\nThought: I should look up France on Wikipedia\\nAction: wikipedia: France\\nPAUSE\\n\\nYou will be called again with this:\\n\\nObservation: France is a country. The capital is Paris.\\n\\nYou then output:\\n\\nAnswer: The capital of France is Paris\"}, {'role': 'user', 'content': 'What does England share borders with? #'}, {'role': 'assistant', 'content': 'Thought: I should look up the neighboring countries of England on Wikipedia.\\n\\nAction: wikipedia: England\\nPAUSE'}, {'role': 'user', 'content': 'Observation: <span class=\"searchmatch\">England</span> is a country that is part of the United Kingdom. The country is located on the island of Great Britain, of which it covers roughly 62%, and over'}, {'role': 'assistant', 'content': 'Thought: I need to find the information about the neighboring countries of England in the Wikipedia summary.\\n\\nAction: wikipedia: Neighbouring countries of England\\nPAUSE'}]\n", " -- running wikipedia Neighbouring countries of England\n", "Observation: <span class=\"searchmatch\">of</span> <span class=\"searchmatch\">countries</span> that have a land border with only one other <span class=\"searchmatch\">country</span>. Some on this list have a maritime border with additional <span class=\"searchmatch\">countries</span>. Some <span class=\"searchmatch\">countries</span>,\n", "Answer: England shares a land border with only one other country, which is Scotland.\n", "[{'role': 'system', 'content': \"You run in a loop of Thought, Action, PAUSE, Observation.\\nAt the end of the loop you output an Answer\\nUse Thought to describe your thoughts about the question you have been asked.\\nUse Action to run one of the actions available to you - then return PAUSE.\\nObservation will be the result of running those actions.\\n\\nYour available actions are:\\n\\ncalculate:\\ne.g. calculate: 4 * 7 / 3\\nRuns a calculation and returns the number - uses Python so be sure to use floating point syntax if necessary\\n\\nwikipedia:\\ne.g. wikipedia: Django\\nReturns a summary from searching Wikipedia\\n\\nsimon_blog_search:\\ne.g. simon_blog_search: Djan<PERSON>\\nSearch <PERSON>'s blog for that term\\n\\nAlways look things up on Wikipedia if you have the opportunity to do so.\\n\\nExample session:\\n\\nQuestion: What is the capital of France?\\nThought: I should look up France on Wikipedia\\nAction: wikipedia: France\\nPAUSE\\n\\nYou will be called again with this:\\n\\nObservation: France is a country. The capital is Paris.\\n\\nYou then output:\\n\\nAnswer: The capital of France is Paris\"}, {'role': 'user', 'content': 'What does England share borders with? #'}, {'role': 'assistant', 'content': 'Thought: I should look up the neighboring countries of England on Wikipedia.\\n\\nAction: wikipedia: England\\nPAUSE'}, {'role': 'user', 'content': 'Observation: <span class=\"searchmatch\">England</span> is a country that is part of the United Kingdom. The country is located on the island of Great Britain, of which it covers roughly 62%, and over'}, {'role': 'assistant', 'content': 'Thought: I need to find the information about the neighboring countries of England in the Wikipedia summary.\\n\\nAction: wikipedia: Neighbouring countries of England\\nPAUSE'}, {'role': 'user', 'content': 'Observation: <span class=\"searchmatch\">of</span> <span class=\"searchmatch\">countries</span> that have a land border with only one other <span class=\"searchmatch\">country</span>. Some on this list have a maritime border with additional <span class=\"searchmatch\">countries</span>. Some <span class=\"searchmatch\">countries</span>,'}, {'role': 'assistant', 'content': 'Answer: England shares a land border with only one other country, which is Scotland.'}]\n"]}], "source": ["#query(\"Has <PERSON> been to Madagascar?\")\n", "query(\"What does England share borders with? #\")"]}, {"cell_type": "code", "execution_count": null, "id": "eedbda9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langchain-new", "language": "python", "name": "langchain-new"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}