('C:\\workspace\\jupyter\\python-test\\dist\\main.exe',
 True,
 <PERSON>alse,
 False,
 'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\workspace\\jupyter\\python-test\\build\\main\\main.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'C:\\workspace\\jupyter\\python-test\\main.py', 'PYSOURCE'),
  ('python312.dll', 'C:\\software\\python\\python312.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'C:\\software\\python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'C:\\software\\python\\Lib\\site-packages\\numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'BINARY'),
  ('select.pyd', 'C:\\software\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\software\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_hashlib.pyd', 'C:\\software\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\software\\python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\software\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\software\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\software\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\software\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\software\\python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\software\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\software\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\software\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\software\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\software\\python\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\software\\python\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\software\\python\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\software\\python\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\software\\python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\software\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\software\\python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\etree.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\_elementpath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\sax.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\objectify.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\html\\diff.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp312-win_amd64.pyd',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\builder.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\software\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\software\\python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\software\\python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\software\\python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\software\\python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'C:\\software\\python\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\software\\python\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\.gradle\\jdks\\eclipse_adoptium-17-amd64-windows\\jdk-17.0.12+7\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'C:\\workspace\\jupyter\\python-test\\build\\main\\base_library.zip',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\software\\python\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\software\\python\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'C:\\software\\python\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\software\\python\\python312.dll')
