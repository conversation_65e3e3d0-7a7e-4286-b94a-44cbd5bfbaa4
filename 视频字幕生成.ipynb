!ffmpeg -i 02.mp4 -q:a 0 -map a audio.mp3
!ffmpeg -i 02.mp4 -vf "subtitles=002_zh.srt" -c:a copy 002.mp4

from http import HTTPStatus
from dashscope.audio.asr import Transcription
import json

# 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将apiKey替换为自己的API Key
import dashscope
dashscope.api_key = "sk-b536c8e6e4ae48d7a52d2bb171ad4119"

task_response = Transcription.async_call(
    model='paraformer-v2',
    file_urls=['https://*************/file/audio.mp3'],
    language_hints=['ja']  # “language_hints”只支持paraformer-v2模型
)

transcribe_response = Transcription.wait(task=task_response.output.task_id)
if transcribe_response.status_code == HTTPStatus.OK:
    # 下载结果文件
    print(json.dumps(transcribe_response.output, indent=4, ensure_ascii=False))

url = transcribe_response.output.results[0]['transcription_url']
# 下载文件
import requests
response = requests.get(url)
file_path = 'video/002'
with open(file_path + '.json', 'wb') as f:
    f.write(response.content)

with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

subtitles = []
for sentence in data['transcripts'][0]['sentences']:
    subtitle = {
        'begin_time': sentence['begin_time'],
        'end_time': sentence['end_time'],
        'text': sentence['text'],
        'sentence_id': sentence['sentence_id']
    }
    subtitles.append(subtitle)

def format_time(milliseconds):
    """
    Convert milliseconds to SRT time format (HH:MM:SS,mmm)
    """
    hours = milliseconds // 3600000
    milliseconds %= 3600000
    minutes = milliseconds // 60000
    milliseconds %= 60000
    seconds = milliseconds // 1000
    milliseconds %= 1000
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

with open(file_path + '.srt', 'w', encoding='utf-8') as f:
    for i, subtitle in enumerate(subtitles, 1):
        # Convert time from milliseconds to SRT format (HH:MM:SS,mmm)
        begin_time = format_time(subtitle['begin_time'])
        end_time = format_time(subtitle['end_time'])
        
        f.write(f"{i}\n")
        f.write(f"{begin_time} --> {end_time}\n")
        f.write(f"{subtitle['text']}\n")
        f.write("\n")





