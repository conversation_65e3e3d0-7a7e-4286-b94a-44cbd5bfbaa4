{"cells": [{"cell_type": "code", "execution_count": 29, "id": "5cb9e893-6b1b-4fd5-bd6b-1ac8efe3ad48", "metadata": {}, "outputs": [], "source": ["import http.client\n", "import json\n", "import os\n", "from openpyxl import load_workbook\n", "import time\n", "import datetime"]}, {"cell_type": "code", "execution_count": 9, "id": "39c576d6-f440-416e-b853-af57be24e78b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'20241126'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["(datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y%m%d')"]}, {"cell_type": "code", "execution_count": 11, "id": "8e20cb02-e5f0-462b-84a9-0bd5309d350d", "metadata": {}, "outputs": [], "source": ["root = os.getcwd()"]}, {"cell_type": "code", "execution_count": 24, "id": "c2a207e4-b737-407f-ab66-cf489c626f1e", "metadata": {}, "outputs": [], "source": ["filename = '%s/刷.xlsx' % root\n", "workbook = load_workbook(filename=filename)"]}, {"cell_type": "code", "execution_count": 19, "id": "8bf87c7b-9a28-4951-8fcd-558d4a15aefb", "metadata": {}, "outputs": [], "source": ["sheets = workbook.sheetnames"]}, {"cell_type": "code", "execution_count": 28, "id": "27873ddb-9b86-47af-a637-119ab5d4ed2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["S23479217626353549121\n", "S23479217622505058204\n"]}], "source": ["for sheet in workbook:\n", "    for row in sheet.rows:\n", "        caseId = row[0].value\n", "        if caseId != None:\n", "            print(row[0].value)\n"]}, {"cell_type": "code", "execution_count": 28, "id": "ef5ccb04-b162-416d-b5d3-a43e1c8bf9bd", "metadata": {}, "outputs": [], "source": ["def check(caseId, session, cookie):\n", "    conn = http.client.HTTPSConnection(\"aps.sz.creditcard.ecitic.com\")\n", "    payload = json.dumps({\n", "       \"start\": 1,\n", "       \"limit\": 30,\n", "       \"queryObj\": \"{\\\"custId\\\":\\\"\" + caseId + \"\\\",\\\"lastPayDate\\\":[\\\"\\\",\\\"\\\"],\\\"lastPayDateStart\\\":\\\"\\\",\\\"lastPayDateEnd\\\":\\\"\\\"}\"\n", "    })\n", "    headers = {\n", "       'Channelid': 'IPCC_OUT',\n", "       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',\n", "       'Pageid': '7151',\n", "       'Store-Session': session,\n", "       'Cookie': cookie,\n", "       'Content-Type': 'application/json',\n", "       'Accept': '*/*',\n", "       'Host': 'aps.sz.creditcard.ecitic.com',\n", "       'Connection': 'keep-alive'\n", "    }\n", "    conn.request(\"POST\", \"/aps-common-gateway/aps-fuse-gateway/aps-openapi-5g/aps-openapi-5g/phone-collect-case/list\", payload, headers)\n", "    res = conn.getresponse()\n", "    data = res.read()\n", "    print(data)\n", "    res_json = json.loads(data.decode(\"utf-8\"))\n", "    if res_json['retCode'] != 0:\n", "        return False\n", "    return True\n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "be04035f-b6cd-416d-862b-a481387e9497", "metadata": {"scrolled": true}, "outputs": [], "source": ["def remark(caseId, remark, session, cookie):\n", "    appointDate = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y%m%d')\n", "    conn = http.client.HTTPSConnection(\"aps.sz.creditcard.ecitic.com\")\n", "    payload = json.dumps({\n", "       \"caseId\": caseId,\n", "       \"appointDate\": appointDate,\n", "       \"taskTypeId\": \"#wo#\",\n", "       \"ipccTaskTypeId\": \"#wo#\",\n", "       \"appointTime\": \"0000\",\n", "       \"actionCodeName\": \"WXCS\",\n", "       \"comment\": remark,\n", "       \"collType\": \"E\",\n", "       \"contactType\": \"A\",\n", "       \"acctId\": \"\",\n", "       \"dueAmt\": \"\",\n", "       \"modelId\": \"TaskPerspective\",\n", "       \"curTimestamp\": int(round(time.time() * 1000)),\n", "       \"callType\": \"O\",\n", "       \"callSrcNumber\": \"\",\n", "       \"callDestNumber\": \"\",\n", "       \"complainInfo\": {\n", "          \"acomplain\": <PERSON><PERSON><PERSON>,\n", "          \"complainIdList\": [],\n", "          \"autoHandleActionCode\": <PERSON><PERSON><PERSON>,\n", "          \"caseId\": caseId,\n", "          \"collType\": \"E\",\n", "          \"contactType\": \"A\",\n", "          \"actionCodeName\": \"WXCS\",\n", "          \"appointDate\": appointDate,\n", "          \"appointTime\": \"0000\",\n", "          \"comment\": remark\n", "       }\n", "    })\n", "    headers = {\n", "       'Channelid': 'IPCC_OUT',\n", "       'Store-Session': session,\n", "       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',\n", "       'Pageid': '7151',\n", "       'Cookie': cookie,\n", "       'Content-Type': 'application/json',\n", "       'Accept': '*/*',\n", "       'Host': 'aps.sz.creditcard.ecitic.com',\n", "       'Connection': 'keep-alive'\n", "    }\n", "    conn.request(\"POST\", \"/aps-common-gateway/aps-fuse-gateway/aps-openapi-5g/aps-openapi-5g/action/submit\", payload, headers)\n", "    res = conn.getresponse()\n", "    data = res.read()\n", "    print(data.decode(\"utf-8\"))"]}, {"cell_type": "code", "execution_count": 9, "id": "d5cac3d1-1305-4d62-9a6b-4703fcc4690f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'C:\\\\workspace\\\\jupyter'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["root"]}, {"cell_type": "code", "execution_count": 31, "id": "2a304420-e16a-4dc2-bdad-b2fb7fc23664", "metadata": {}, "outputs": [], "source": ["with open(root + '/config.txt', 'r') as f:\n", "    contents = f.readlines()\n", "config = {}\n", "for content in contents:\n", "    arr = content.split(\"==\")\n", "    config[arr[0]] = arr[1].replace('\\n', '').replace('\\r', '')"]}, {"cell_type": "code", "execution_count": 15, "id": "c39f5e4e-1652-483a-9e8e-b1e58db73a6d", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Store-Session=bb\\n', 'Cookie=aa']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["contents"]}, {"cell_type": "code", "execution_count": 32, "id": "08c88978-1740-4075-81a7-c34cb9c3b785", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Store-Session': 'e9f2b137494ccaf32bf82c244b32e721', '<PERSON>ie': '_bm_id.dc3d'}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["config"]}, {"cell_type": "code", "execution_count": 30, "id": "e3a4c70b-eb33-49ca-ac15-45d2c6bdde8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"retCode\":\"4003\",\"retMsg\":\"\\xe6\\x9c\\xaa\\xe7\\x99\\xbb\\xe9\\x99\\x86\"}'\n"]}, {"data": {"text/plain": ["False"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["check('S23479217626353549121', config['Store-Session'], config['<PERSON><PERSON>'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}